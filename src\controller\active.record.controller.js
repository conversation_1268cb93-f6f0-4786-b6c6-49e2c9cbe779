const fs = require('fs')
const path = require('path')

// 获取数据
async function getActiveData(ctx, next) {
  try {
    const { time } = ctx.query
    let fileName
    if (!time) {
      const time = new Date().toLocaleString().replace(/\//g, '-')
      fileName = time.toLocaleString('zh-CN', { hour12: false }).split('-').slice(0, 2).join('-') + '.json'
    } else {
      fileName = time.replace('/', '-') + '.json'
    }

    const filePath = path.resolve(__dirname, `../assets/data/ActiveUser/${fileName}`)
    const isExists = fs.existsSync(filePath)
    if (!isExists) return (ctx.body = { code: 400, message: '数据不存在' }) // 查询文件是否存在
    const fileContent = await fs.promises.readFile(filePath, 'utf8') // 读取文件
    ctx.data = JSON.parse(fileContent).record
    next()
  } catch (error) {
    ctx.body = { code: 500, message: '获取失败', data: error.message }
  }
}

// 记录活跃用户
async function activeRecord(ctx) {
  try {
    const val = ctx.request.body
    const time = new Date().toLocaleString().replace(/\//g, '-')
    val.time = time
    const fileName = time.toLocaleString('zh-CN', { hour12: false }).split('-').slice(0, 2).join('-') + '.json'
    const filePath = path.resolve(__dirname, `../assets/data/ActiveUser/${fileName}`)
    // 查询文件是否存在
    const isExists = fs.existsSync(filePath)
    if (!isExists) fs.writeFileSync(filePath, JSON.stringify({ record: [] })) // 创建文件

    const fileContent = await fs.promises.readFile(filePath, 'utf8')
    const Content = JSON.parse(fileContent)
    Content.record.push(val)
    await fs.promises.writeFile(filePath, JSON.stringify(Content))

    ctx.body = { code: 200, data: Content.record, message: '成功' }
  } catch (error) {
    ctx.body = { code: 400, message: '添加失败', data: error.message }
  }
}

// 记录查询
async function gainRecord(ctx) {
  try {
    ctx.body = { code: 200, message: '成功', data: ctx.data }
  } catch (error) {
    ctx.body = { code: 400, message: '失败', data: error.message }
  }
}

// 读取记录列表
async function gainRecordList(ctx) {
  const YY = new Date().getFullYear()
  const { year = YY } = ctx.query
  try {
    const data = {}
    const files = fs.readdirSync(path.resolve(__dirname, '../assets/data/ActiveUser')).filter((i) => i.includes(year))
    for (let i = 0; i < files.length; i++) {
      const fileContent = await fs.promises.readFile(path.resolve(__dirname, `../assets/data/ActiveUser/${files[i]}`), 'utf8')
      data[files[i].replace('.json', '')] = JSON.parse(fileContent).record.length
    }
    ctx.body = { code: 200, message: '成功', data }
  } catch (error) {
    ctx.body = { code: 400, message: '失败', data: error.message }
  }
}

// 获取活跃用户折线图数据
async function gainActiveLine(ctx) {
  try {
    const data = {}
    const time = ctx.query.time ? ctx.query.time.replace('/', '-') : ''
    const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate() // 获取当前月份有几天
    const currentDay = new Date().getDate() // 获取当前几号
    const length = time ? daysInMonth : currentDay
    for (let i = 1; i <= length; i++) {
      data[i] = data[i] ? data[i] : 0
    }
    ctx.data.forEach((i) => {
      const key = i.time.split(' ')[0].split('-')[2]
      data[key] = data[key] ? data[key] + 1 : 1
    })

    ctx.body = { code: 200, message: '成功', data, V: ctx.data }
  } catch (error) {
    ctx.body = { code: 400, message: '获取失败', data: error.message }
  }
}

// 获取活跃用户
async function gainActiveUser(ctx) {
  try {
    const data = {}
    ctx.data.forEach(({ userName }) => {
      data[userName] = data[userName] ? data[userName] + 1 : 1
    })
    ctx.body = { code: 200, message: '成功', data }
  } catch (error) {
    ctx.body = { code: 400, message: '获取失败', data: error.message }
  }
}

// 获取某个活跃用户折线图数据
async function gainActiveUserLine(ctx) {
  try {
    const { userName } = ctx.query
    const data = {}
    const time = ctx.query.time ? ctx.query.time.replace('/', '-') : ''
    const daysInMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate() // 获取当前月份有几天
    const currentDay = new Date().getDate() // 获取当前几号
    const length = time ? daysInMonth : currentDay
    for (let i = 1; i <= length; i++) {
      data[i] = data[i] ? data[i] : 0
    }
    ctx.data
      .filter((i) => i.userName === userName)
      .forEach((i) => {
        const key = i.time.split(' ')[0].split('-')[2]
        data[key] = data[key] ? data[key] + 1 : 1
      })

    ctx.body = { code: 200, message: '成功', data }
  } catch (error) {
    ctx.body = { code: 400, message: '获取失败', data: error.message }
  }
}

module.exports = { getActiveData, activeRecord, gainRecord, gainRecordList, gainActiveLine, gainActiveUser, gainActiveUserLine }
