const excelJS = require('exceljs')
const path = require('path')
const fs = require('fs')

const preserveKey = require('../assets/json/preserveKey.json')
const imagePath = (fileName) => path.resolve('C:\\Down\\Pic', fileName)

function insertionImage(workbook, worksheet) {
  return (strImages, row, col, length = 2) => {
    if (!strImages || !strImages.length) return
    const images = strImages.split(',').filter(Boolean)
    images.forEach((image, index) => {
      if (index >= length) return
      const imageId = workbook.addImage({ filename: imagePath(image), extension: path.extname(image) })
      worksheet.addImage(imageId, { tl: { col: col + index, row: row }, ext: { width: 40, height: 40 }, editAs: 'oneCell' })
    })
  }
}

class ExportFile {
  project(ctx) {
    const { list } = ctx.request.body
    ctx.body = { code: 200, data: list }
  }

  //   gis维护导出
  async preserveGis(ctx) {
    try {
      const { list } = ctx.request.body
      const { fileName } = ctx.query
      const workbook = new excelJS.Workbook()
      const worksheet = workbook.addWorksheet('Sheet1')
      const insertion = insertionImage(workbook, worksheet)
      // 添加表头
      worksheet.columns = Object.entries(preserveKey).map(([key, value]) => ({ header: value, key, width: 30 }))
      // 填充数据
      list.forEach((item, index) => {
        worksheet.addRow(item)
        insertion(item.path1, index + 1, 5)
        insertion(item.path2, index + 1, 7)
        insertion(item.path3, index + 1, 12)
        insertion(item.path4, index + 1, 14, 1)
      })
      // 保存文件
      const buffer = await workbook.xlsx.writeBuffer()
      const name = fileName ?? new Date().getTime()
      const newFileName = path.join(__dirname, `../../updates/file/${name}.xlsx`)
      fs.writeFileSync(newFileName, buffer)
      ctx.body = { code: 200, message: '导出成功', data: { https: `${ctx.origin}/nodeServer/file/${name}.xlsx`, http: `http://47.106.81.163:3001/file/${name}.xlsx` } }
    } catch (error) {
      ctx.body = { code: 400, message: '导出失败', data: error.message }
    }
  }
}

module.exports = new ExportFile()
