const Router = require('@koa/router')
const router = new Router({ prefix: '/resource' })
const controller = require('../../controller/resource.management.controller.js')

// 公共资源管理

router.post('/analysisDispose', controller.analysisDispose) // 解析pdf文件
router.post('/coordinateTransformation', controller.coordinateTransformation) // 坐标转换
router.post('/export/project', controller.exportProject) // 导出小额项目
router.post('/export/preserve', controller.exportPreserve) // 导出gis维护
router.post('/export/fieldWork', controller.exportFieldWork) // 导出外业数据
router.post('/upload/:baseUrl', controller.uploadFile, controller.uploadFileSucceed) // 文件上传
router.get('/readFiles/:baseUrl', controller.readFileList) // 读取文件列表
router.get('/ossVoucher', controller.ossVoucher) // 获取oss 凭证

module.exports = router
