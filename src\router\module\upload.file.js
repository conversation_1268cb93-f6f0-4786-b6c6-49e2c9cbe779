const Router = require('@koa/router')
const router = new Router({ prefix: '/upload' })

const { analysisDispose, renderUploadMessage, pumpHouseFileUpload, renderPumpHouseFileUploadMessage, gainPumpHouseFileCatalog } = require('../../controller/upload.file.controller.js')
router.post('/', analysisDispose, renderUploadMessage)
router.post('/pump_house/:id', pumpHouseFileUpload, renderPumpHouseFileUploadMessage)
router.get('/pump_house/:id', gainPumpHouseFileCatalog)

module.exports = router
