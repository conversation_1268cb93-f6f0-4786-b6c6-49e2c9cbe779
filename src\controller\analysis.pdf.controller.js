const fs = require('fs')
const path = require('path')
const pdf = require('pdf-parse')
const mammoth = require('mammoth')

async function analysis(ctx) {
  try {
    let data
    const { url } = ctx.query
    if (!url) return (ctx.body = { code: 400, message: '缺少文件路径' })
    const ext = path.extname(url)
    if (!['.docx', '.pdf'].includes(ext)) return (ctx.body = { code: 400, message: `暂不支持${ext}格式` })
    const filePath = path.resolve(__dirname, `../../updates/${url}`)
    const isExists = fs.existsSync(filePath)
    let dataBuffer = fs.readFileSync(filePath)
    if (!isExists) return (ctx.body = { code: 500, message: '文件不存在' })

    if (ext === '.pdf') {
      const { text } = await pdf(dataBuffer) //  解析 pdf
      data = text
    } else {
      const { value } = await mammoth.extractRawText({ buffer: dataBuffer }) //解析 word 文档
      data = value
    }

    ctx.body = { code: 200, message: '解析成功', data }
  } catch (error) {
    ctx.body = { code: 500, message: '解析失败', data: error.message }
  }
}

module.exports = { analysis }
