const { sql, query, queryWithParams } = require('../service/index.js')

class FieldWorkController {
  // 维护查询列表
  async list(ctx, next) {
    try {
      const { finish, page = 1, pageSize = 10 } = ctx.request.query
      const pageNum = Number(page) > 0 ? Number(page) : 1
      const sizeNum = Number(pageSize) > 0 ? Number(pageSize) : 10
      let sentence
      let sentenceSentence
      const txt = `Id, Facility_Type, INPUTSTAFF, INPUTDATE, Task_Type, Zone_Code, Remark1, X, Y`

      if (finish == `true`) {
        sentenceSentence = `SELECT COUNT(*) as total FROM dbo.[OperationFeedbacks] WHERE Path4 IS NOT NULL AND Path4 <> ''`
        sentence = `SELECT ${txt} FROM dbo.[OperationFeedbacks] WHERE Path4 IS NOT NULL AND Path4 <> '' ORDER BY INPUTDATE DESC OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`
      } else if (finish == `false`) {
        sentenceSentence = `SELECT COUNT(*) as total FROM dbo.[OperationFeedbacks] WHERE COALESCE(Path4, '') = ''`
        sentence = `SELECT ${txt} FROM dbo.[OperationFeedbacks] WHERE COALESCE(Path4, '') = '' ORDER BY INPUTDATE DESC OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`
      } else if (finish == 'all') {
        sentenceSentence = `SELECT COUNT(*) as total FROM dbo.[OperationFeedbacks]`
        sentence = `SELECT ${txt} FROM dbo.[OperationFeedbacks]`
      } else {
        sentenceSentence = `SELECT COUNT(*) as total FROM dbo.[OperationFeedbacks]`
        sentence = `SELECT ${txt} FROM dbo.[OperationFeedbacks] ORDER BY INPUTDATE DESC OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`
      }

      // 查询总数
      const { recordsets: sentenceSets } = await queryWithParams(sentenceSentence)
      const total = sentenceSets[0][0].total
      // 查询分页数据
      const offset = (pageNum - 1) * sizeNum
      const { recordsets } = await queryWithParams(sentence, {
        offset: { type: sql.Int, value: offset },
        sizeNum: { type: sql.Int, value: sizeNum }
      })
      ctx.body = {
        code: 200,
        msg: '查询成功',
        data: { total, list: recordsets[0], page: pageNum, pageSize: sizeNum }
      }
    } catch (error) {
      ctx.body = { code: 500, mgs: '查询失败', error: error.message }
    }
  }

  // 维护数据统计
  async statistics(ctx, next) {
    try {
      const s1 = `SELECT COUNT(*) as total FROM dbo.[OperationFeedbacks] WHERE Path4 IS NOT NULL AND Path4 <> ''`
      const s2 = `SELECT COUNT(*) as total FROM dbo.[OperationFeedbacks] WHERE COALESCE(Path4, '') = ''`
      const [r1, r2] = await Promise.all([queryWithParams(s1), queryWithParams(s2)])
      const countFinish = r1.recordsets[0][0].total
      const countUndone = r2.recordsets[0][0].total
      ctx.body = { code: 200, msg: '查询成功', data: { countFinish, countUndone } }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }

  // 获取详情
  async gain(ctx, next) {
    try {
      const { id } = ctx.request.query
      if (!id) return (ctx.body = { code: 400, msg: '查询失败', error: '未接收到id 参数' })
      const sentence = `SELECT * FROM dbo.[OperationFeedbacks] WHERE ID = @id`
      const { recordsets } = await queryWithParams(sentence, { id: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}

module.exports = new FieldWorkController()
