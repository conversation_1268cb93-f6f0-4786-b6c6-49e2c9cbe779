const { sql, query, queryWithParams } = require('../service/index.js')

class PushMessageController {
  // 查询设备Cid
  async check(ctx) {
    try {
      const user = ctx.user
      const { cid: newCid } = ctx.query
      if (!newCid) return (ctx.body = { code: 400, msg: '参数cid错误' })
      if (user.Cid !== newCid) {
        const sentence = `UPDATE dbo.[User] SET Cid = @Cid WHERE id = @id`
        await queryWithParams(sentence, { Cid: { type: sql.VarChar, value: newCid }, id: { type: sql.Int, value: user.id } })
      }
      ctx.body = { code: 200, message: 'Cid查询成功', data: newCid }
    } catch (error) {
      ctx.body = { code: 500, message: '查询失败', data: error.message }
    }
  }
  // 查询派送列表
  async deliveryList(ctx) {
    try {
      const user = ctx.user
      const { crew } = ctx.query
      const sentence = `SELECT * FROM dbo.[User] WHERE Cid IS NOT NULL AND Cid <> @Cid`
      const { recordsets } = await queryWithParams(sentence, { Cid: { type: sql.VarChar, value: user.Cid } })
      const CIds = recordsets[0].map((item) => item.Cid)
      const data = crew !== 'true' ? [...new Set(CIds)] : recordsets[0].map(({ Cid, Name }) => ({ Name, Cid }))
      ctx.body = { code: 200, message: '查询成功', data }
    } catch (error) {
      ctx.body = { code: 500, message: '查询失败', data: error.message }
    }
  }
}

module.exports = new PushMessageController()
