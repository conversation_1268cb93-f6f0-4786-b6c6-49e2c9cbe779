const jwt = require('jsonwebtoken')
const { sql, query, queryWithParams } = require('../service/index.js')

const verifyWhite = require('../assets/json/verify.white.list.json')
class VerifyMiddleware {
  // 登录验证
  async verifyAuth(ctx, next) {
    const { url, method } = ctx.request
    const authorization = ctx.headers.authorization
    const pass = verifyWhite.list.find((item) => new RegExp(item).test(url))
    if (!pass) {
      if (!authorization) return (ctx.body = { code: 401, message: '未登录' })
      const token = authorization.replace('Bearer ', '')
      // 解析token
      let result
      try {
        result = jwt.verify(token, process.env.JWT_SECRET, { algorithms: ['HS256'] })
      } catch (error) {
        return (ctx.body = { code: 401, message: 'token验证失败', data: error.message })
      }
      const id = result['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier']
      const userName = result['http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name']
      const sentence = `SELECT * FROM dbo.[User] WHERE id = @id AND Username = @Username`
      const { recordsets } = await queryWithParams(sentence, { id: { type: sql.Int, value: id }, Username: { type: sql.NVarChar, value: userName } })
      if (!recordsets[0].length) return (ctx.body = { code: 401, message: '未知用户' })
      ctx.user = recordsets[0][0]
      await next()
    } else {
      await next()
    }
  }
}

module.exports = new VerifyMiddleware()
