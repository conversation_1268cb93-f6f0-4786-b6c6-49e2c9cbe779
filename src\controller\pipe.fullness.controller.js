const { sql, query, queryWithParams } = require('../service/index.js')

class PipeFullnessController {
  /**
   * 查询指定日期的管道液位数据，按 SystemId 分组
   * @param {import('koa').Context} ctx - Koa 上下文对象，query 需包含 time（日期字符串，格式如 2025-04-09）
   * @param {Function} next - Koa next
   * @returns {Promise<void>}
   */
  async gain(ctx, next) {
    try {
      const { time } = ctx.query
      if (!time) return (ctx.body = { code: 400, msg: '缺少参数 time', data: null })

      // 查询指定日期的数据，在 SQL 层面按 SystemId 分组
      const sentence = `
        SELECT
          SystemId,
          DotName,
          PipeDiameter,
          @time as Time,
          JSON_QUERY('[' + STRING_AGG(
            JSON_QUERY('{"DataTime":"' + CONVERT(varchar(19), DataTime, 120) + '","LiquidLevel":' + CAST(LiquidLevel as varchar) + '}'),
            ','
          ) + ']') as LiquidLevels,
          JSON_QUERY('[' + STRING_AGG(CAST(Elevation as varchar), ',') + ']') as Elevations
        FROM dbo.[PipeFullness]
        WHERE CONVERT(varchar(10), DataTime, 120) = @time
        GROUP BY SystemId, DotName, PipeDiameter
        ORDER BY SystemId
      `
      const { recordsets } = await queryWithParams(sentence, { time: { type: sql.DateTime, value: time } })
      const rows = recordsets[0]

      // 解析 JSON 字符串并处理数据
      const groupedData = rows.map((row) => ({
        SystemId: row.SystemId,
        DotName: row.DotName,
        PipeDiameter: row.PipeDiameter,
        Time: row.Time,
        LiquidLevels: JSON.parse(row.LiquidLevels || '[]'),
        Elevations: JSON.parse(row.Elevations || '[]')
      }))
      // LiquidLevels 按时间排序，Elevations 保持顺序
      const result = groupedData.map((item) => {
        // 先将 LiquidLevels 按小时分组
        const hourMap = new Map()
        for (const l of item.LiquidLevels) {
          const hour = new Date(l.DataTime).getHours()
          hourMap.set(hour, l.LiquidLevel)
        }
        // 构建 0~23 小时的 LiquidLevels，缺失补 0
        const fullLiquidLevels = []
        for (let h = 0; h < 24; h++) {
          fullLiquidLevels.push(hourMap.get(h) ?? 0)
        }
        return { ...item, LiquidLevels: fullLiquidLevels }
      })
      ctx.body = { code: 200, msg: '查询成功', data: result }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', data: error.message }
    }
  }

  // 查询数据时间段
  async scope(ctx, next) {
    try {
      const minMaxResult = await query('SELECT MIN(DataTime) AS minTime, MAX(DataTime) AS maxTime FROM dbo.[PipeFullness]')
      const minTime = minMaxResult.recordset[0].minTime
      const maxTime = minMaxResult.recordset[0].maxTime
      ctx.body = { code: 200, msg: '查询成功', data: { minTime, maxTime } }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', data: error.message }
    }
  }
}

module.exports = new PipeFullnessController()
