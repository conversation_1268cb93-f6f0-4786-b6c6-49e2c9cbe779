const eviltransform = require('eviltransform')

class TransformCoordinate {
  transform(ctx) {
    try {
      const { lat, lng, fn = 'wgs2gcj' } = ctx.request.body
      if (!lat || !lng) return (ctx.body = { code: 400, message: '缺少参数' })
      const latLng = eviltransform[fn](lat, lng)
      ctx.body = { code: 200, data: latLng }
    } catch (error) {
      ctx.body = { code: 500, message: error.message }
    }
  }
}

module.exports = new TransformCoordinate()
