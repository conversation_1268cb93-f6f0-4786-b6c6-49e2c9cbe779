const { sql, queryWithParams } = require('../service/index.js')

class DictionariesController {
  async create(ctx, next) {
    try {
      const { DictType, DictCode, DictValue, Description } = ctx.request.body
      if (!DictType || !DictCode || !DictValue) return (ctx.body = { code: 400, msg: '参数缺失' })
      await queryWithParams('INSERT INTO dbo.[SystemDictionary] (DictType, DictCode, DictValue, Description) VALUES (@DictType, @DictCode, @DictValue, @Description)', {
        DictType: { type: sql.VarChar, value: DictType },
        DictCode: { type: sql.Var<PERSON>har, value: DictCode },
        DictValue: { type: sql.VarChar, value: DictValue },
        Description: { type: sql.VarChar, value: Description }
      })
      ctx.body = { code: 200, msg: '创建成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '创建失败', error: error.message }
    }
  }
  async delete(ctx, next) {
    try {
      const { id } = ctx.params
      if (!id) return (ctx.body = { code: 400, msg: '参数缺失' })
      await queryWithParams('DELETE FROM dbo.[SystemDictionary] WHERE Id = @Id', { Id: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '删除成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '删除失败' }
    }
  }
  async update(ctx, next) {
    try {
      const { Id, DictCode, DictValue, Description, SortOrder, IsEnabled } = ctx.request.body
      if (!Id || !DictCode || !DictValue) return (ctx.body = { code: 400, msg: '参数缺失' })
      await queryWithParams(
        `UPDATE dbo.[SystemDictionary] SET 
          DictCode = @DictCode, 
          DictValue = @DictValue, 
          SortOrder = @SortOrder, 
          IsEnabled = @IsEnabled, 
          Description = @Description 
          WHERE Id = @Id`,
        {
          Id: { type: sql.Int, value: Id },
          DictCode: { type: sql.VarChar, value: DictCode },
          DictValue: { type: sql.VarChar, value: DictValue },
          SortOrder: { type: sql.Int, value: SortOrder },
          IsEnabled: { type: sql.Int, value: IsEnabled },
          Description: { type: sql.VarChar, value: Description }
        }
      )
      ctx.body = { code: 200, msg: '更新成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '更新失败' }
    }
  }
  async gain(ctx, next) {
    try {
      const { type } = ctx.params
      if (!type) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await queryWithParams('SELECT * FROM dbo.[SystemDictionary] WHERE DictType = @DictType AND IsEnabled = 1', { DictType: { type: sql.VarChar, value: type } })
      ctx.body = { code: 200, msg: '获取成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '获取失败' }
    }
  }
  async list(ctx, next) {
    try {
      const { type } = ctx.params
      if (!type) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await queryWithParams('SELECT * FROM dbo.[SystemDictionary] WHERE DictType = @DictType', { DictType: { type: sql.VarChar, value: type } })
      ctx.body = { code: 200, msg: '获取成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '获取失败' }
    }
  }
  async typeList(ctx, next) {
    try {
      const { recordsets } = await queryWithParams('SELECT DISTINCT DictType, Description FROM dbo.[SystemDictionary]')
      ctx.body = { code: 200, msg: '获取成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, msg: '获取失败' }
    }
  }
}

module.exports = new DictionariesController()
