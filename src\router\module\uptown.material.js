const Router = require('@koa/router')
const router = new Router({ prefix: '/uptown_material' })

// 小区档案
const controller = require('../../controller/uptown.material.controller.js')

router.get('/', controller.uptownMaterial.gain) // 获取小区档案详情
router.get('/list', controller.uptownMaterial.list) // 档案列表
router.get('/codes', controller.uptownMaterial.codes) // 获取有档案的小区编码
router.get('/zone', controller.uptownMaterial.zone) // 查询档案对应的无缝区块

// 水池箱档案
router.get('/pool_tank', controller.poolTank.gain) // 获取水池箱详情
router.post('/pool_tank', controller.poolTank.create) // 获取水池箱详情
router.put('/pool_tank', controller.poolTank.update) // 更新水池箱详情

module.exports = router
