const { sql, query, queryWithParams } = require('../service/index.js')
const { createVersionCode } = require('../utils/index.js')

class AppVersionControlController {
  async create(ctx) {
    try {
      const { version, updateContent, allowUptateTime, allowUptate, downUrl, isTest = 1 } = ctx.request.body

      // 查询是否存在相同版本号的记录
      const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[AppVersion] WHERE version = @version`, { version: { type: sql.VarChar, value: version } })
      const existVersion = recordsets[0]
      if (existVersion.length > 0) return (ctx.body = { code: 400, msg: '版本号已存在' })

      const versionCode = createVersionCode(version)
      const sentence = `INSERT INTO dbo.[AppVersion]
      (version,updateContent,allowUptateTime,allowUptate,downUrl,isTest,versionCode) VALUES
      (@version, @updateContent, @allowUptateTime, @allowUptate, @downUrl, @isTest, @versionCode)`
      await queryWithParams(sentence, {
        version: { type: sql.VarChar, value: version },
        updateContent: { type: sql.VarChar, value: updateContent },
        allowUptateTime: { type: sql.VarChar, value: allowUptateTime },
        allowUptate: { type: sql.Int, value: allowUptate },
        downUrl: { type: sql.VarChar, value: downUrl },
        versionCode: { type: sql.Int, value: versionCode },
        isTest: { type: sql.Int, value: isTest }
      })
      ctx.body = { code: 200, msg: '创建成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '创建失败1212', error: error.message }
    }
  }
  async delete(ctx) {
    try {
      const { id } = ctx.request.query
      if (!id) return (ctx.body = { code: 400, msg: '参数缺失' })
      const sentence = `DELETE FROM dbo.[AppVersion] WHERE id = @id`
      await queryWithParams(sentence, { id: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '删除成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '删除失败', error: error.message }
    }
  }
  async update(ctx) {
    try {
      const { id, updateContent, allowUptateTime, allowUptate, downUrl, isTest } = ctx.request.body
      // 查询是否存在相同版本号的记录
      const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[AppVersion] WHERE id = @id`, { id: { type: sql.Int, value: id } })
      const existVersion = recordsets[0]
      if (existVersion.length === 0) return (ctx.body = { code: 400, msg: '版本号不存在' })

      const sentence = `UPDATE dbo.[AppVersion] SET 
      updateContent = @updateContent, allowUptateTime = @allowUptateTime, allowUptate = @allowUptate, downUrl = @downUrl, isTest = @isTest 
      WHERE id = @id`
      await queryWithParams(sentence, {
        id: { type: sql.Int, value: id },
        updateContent: { type: sql.VarChar, value: updateContent },
        allowUptateTime: { type: sql.VarChar, value: allowUptateTime },
        allowUptate: { type: sql.Int, value: allowUptate },
        downUrl: { type: sql.VarChar, value: downUrl },
        isTest: { type: sql.Int, value: isTest }
      })
      ctx.body = { code: 200, msg: '更新成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '更新失败', error: error.message }
    }
  }
  async list(ctx) {
    try {
      const sentence = `SELECT * FROM dbo.[AppVersion] ORDER BY versionCode DESC`
      const { recordsets } = await queryWithParams(sentence, {})
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
  async compare(ctx) {
    const { version } = ctx.request.query
    try {
      if (!version) return (ctx.body = { code: 400, msg: '参数错误' })
      const versionCode = createVersionCode(version)
      const sentence = `SELECT TOP 1 * FROM dbo.[AppVersion] WHERE allowUptate = 1 ORDER BY versionCode DESC`
      const { recordsets } = await queryWithParams(sentence, {})
      const latestVersion = recordsets[0][0]
      const isReachTime = 60 * 60 * 8 * 1000 + new Date().getTime() >= new Date(latestVersion.allowUptateTime).getTime()
      const renewable = versionCode < latestVersion.versionCode && isReachTime
      const data = { renewable, latestVersion }
      ctx.body = { code: 200, msg: '查询成功', isReachTime, data }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}

module.exports = new AppVersionControlController()
