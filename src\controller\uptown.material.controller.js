const { sql, query, queryWithParams } = require('../service/index.js')
// 小区档案管理
class UptownMaterialController {
  async gain(ctx) {
    const { code } = ctx.request.query
    let waterTankArchives = null
    let pumpHouseList = null
    try {
      const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[dwd_gwyy_xqxttz] WHERE xqbm = @code`, { code: { type: sql.VarChar, value: code } })
      if (recordsets[0][0]) {
        const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[water_tank_archives] WHERE xqbm = @code`, { code: { type: sql.VarChar, value: code } })
        waterTankArchives = recordsets[0][0]
        const { recordsets: res } = await queryWithParams(`SELECT PumpRoomNumber, ProgressStatus, RemouldState, PumpHouseName FROM dbo.[SecondaryWaterProgressNew] WHERE xqbm = @code`, { code: { type: sql.Var<PERSON>har, value: code } })
        pumpHouseList = res[0]
      }
      ctx.body = { code: 200, data: recordsets[0][0], waterTankArchives, pumpHouseList }
    } catch (error) {
      ctx.body = { code: 500, error: error.message }
    }
  }

  async list(ctx) {
    try {
      const { xqmc, sws, exist, page = 1, pageSize = 15 } = ctx.request.query
      const pageNum = parseInt(page)
      const pageSizeNum = parseInt(pageSize)
      const offset = (pageNum - 1) * pageSizeNum

      const field = `xqbm, xqmc, is_pump_room, sws, xqlb, ssjd,
        CASE WHEN EXISTS (SELECT 1 FROM dbo.[water_tank_archives] WHERE water_tank_archives.xqbm = dwd_gwyy_xqxttz.xqbm)
             THEN CAST(1 AS BIT)
             ELSE CAST(0 AS BIT)
        END AS hasWaterTankArchive`
      const where = `${
        xqmc
          ? 'WHERE xqmc LIKE @xqmc'
          : sws && exist
          ? `WHERE sws LIKE @sws AND is_pump_room ${exist == '是' ? `NOT LIKE '%否%'` : `LIKE '%否%'`}`
          : sws
          ? 'WHERE sws LIKE @sws'
          : exist
          ? `WHERE is_pump_room ${exist == '是' ? `NOT LIKE '%否%'` : `LIKE '%否%'`}`
          : ''
      }`

      // 获取总数的查询
      const countSentence = `SELECT COUNT(*) as total FROM dbo.[dwd_gwyy_xqxttz] ${where}`
      const { recordsets: countResult } = await queryWithParams(countSentence, {
        sws: { type: sql.VarChar, value: `%${sws}%` },
        xqmc: { type: sql.VarChar, value: `%${xqmc}%` }
      })
      const total = countResult[0][0].total

      const sentence = `SELECT ${field} FROM dbo.[dwd_gwyy_xqxttz] ${where} ORDER BY xqbm OFFSET @offset ROWS FETCH NEXT @pageSize ROWS ONLY`

      const { recordsets } = await queryWithParams(sentence, {
        sws: { type: sql.VarChar, value: `%${sws}%` },
        xqmc: { type: sql.VarChar, value: `%${xqmc}%` },
        offset: { type: sql.Int, value: offset },
        pageSize: { type: sql.Int, value: pageSizeNum }
      })

      // 计算分页信息
      const totalPages = Math.ceil(total / pageSizeNum)

      ctx.body = {
        code: 200,
        msg: '查询成功',
        data: recordsets[0],
        pagination: {
          page: pageNum,
          pageSize: pageSizeNum,
          total,
          totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        }
      }
    } catch (error) {
      ctx.body = { code: 500, error: error.message }
    }
  }

  async codes(ctx) {
    try {
      const sentence = `SELECT Zone_Code FROM [dbo].[DIST_ADDRESS]  where Client_Name is not NULL`
      const { recordsets } = await queryWithParams(sentence, {})
      const list = [...new Set(recordsets[0].map((item) => item.Zone_Code))]
      ctx.body = { code: 200, msg: '查询成功', data: { total: list.length, list } }
    } catch (error) {
      ctx.body = { code: 500, error: error.message }
    }
  }

  async zone(ctx) {
    try {
      const { xqbm } = ctx.query
      if (!xqbm) return (ctx.body = { code: 400, error: '缺少参数xqbm' })
      const sentence = `SELECT * FROM [dbo].[DIST_ADDRESS]  where Client_Name LIKE @xqbm`
      const { recordsets } = await queryWithParams(sentence, { xqbm: { type: sql.VarChar, value: `%${xqbm}%` } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}

// 水池箱档案管理
class PoolTankController {
  async gain(ctx) {
    try {
      const { xqbm } = ctx.request.query
      const sentence = `SELECT * FROM dbo.[water_tank_archives] WHERE xqbm = @xqbm`
      const { recordsets } = await queryWithParams(sentence, { xqbm: { type: sql.VarChar, value: xqbm } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      console.error(error.message)
      ctx.body = { code: 500, error: '查询失败' }
    }
  }

  async create(ctx) {
    const { standard_area_name, area_category, water_office, street_office, pool_area_name, xqbm } = ctx.request.body
    try {
      if (!standard_area_name || !area_category || !water_office || !street_office || !pool_area_name || !xqbm) return (ctx.body = { code: 400, error: '缺少参数' })
      const sentence = `INSERT INTO dbo.[water_tank_archives] (standard_area_name, area_category, water_office, street_office, pool_area_name, xqbm, total_count) VALUES (@standard_area_name, @area_category, @water_office, @street_office, @pool_area_name, @xqbm, @total_count)`
      await queryWithParams(sentence, {
        standard_area_name: { type: sql.VarChar, value: standard_area_name },
        area_category: { type: sql.VarChar, value: area_category },
        water_office: { type: sql.VarChar, value: water_office },
        street_office: { type: sql.VarChar, value: street_office },
        pool_area_name: { type: sql.VarChar, value: pool_area_name },
        xqbm: { type: sql.VarChar, value: xqbm },
        total_count: { type: sql.Int, value: 0 }
      })
      const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[water_tank_archives] WHERE xqbm = @xqbm`, { xqbm: { type: sql.VarChar, value: xqbm } })
      ctx.body = { code: 200, data: recordsets[0][0], msg: '创建成功' }
    } catch (error) {
      console.error(error.message)
      ctx.body = { code: 500, error: '创建失败' }
    }
  }

  async update(ctx) {
    let length = 0
    const { location, capacity, type, material, quantity, xqbm } = ctx.request.body
    try {
      if (!xqbm) return (ctx.body = { code: 400, error: '缺少参数 xqbm' })
      const every = [location, capacity, type, material, quantity].every((item) => {
        const L = item.split(';').filter(Boolean).length
        if (length === 0) length = L
        return L === length
      })
      if (!every) return (ctx.body = { code: 400, error: '参数长度不一致' })
      const total_count = quantity.split(';').reduce((acc, cur) => acc + parseInt(cur), 0) // 总数
      const sentence = `UPDATE dbo.[water_tank_archives] SET location = @location, capacity = @capacity, type = @type, material = @material, quantity = @quantity, update_person = @update_person, total_count = @total_count WHERE xqbm = @xqbm`
      await queryWithParams(sentence, {
        location: { type: sql.VarChar, value: location },
        capacity: { type: sql.VarChar, value: capacity },
        type: { type: sql.VarChar, value: type },
        material: { type: sql.VarChar, value: material },
        quantity: { type: sql.VarChar, value: quantity },
        total_count: { type: sql.Int, value: total_count },
        update_person: { type: sql.VarChar, value: ctx.user.Name },
        xqbm: { type: sql.VarChar, value: xqbm }
      })

      const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[water_tank_archives] WHERE xqbm = @xqbm`, { xqbm: { type: sql.VarChar, value: xqbm } })
      ctx.body = { code: 200, data: recordsets[0][0], msg: '修改成功' }
    } catch (error) {
      console.error(error.message)
      ctx.body = { code: 500, error: '修改失败' }
    }
  }
}

module.exports = { uptownMaterial: new UptownMaterialController(), poolTank: new PoolTankController() }
