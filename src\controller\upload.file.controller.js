const { koaBody } = require('koa-body') //参数解析
const { join } = require('path')
const mime = require('mime-types')
const fs = require('fs')

async function analysisDispose(ctx, next) {
  try {
    const { fileType = 'file' } = ctx.query
    const dirPath = join(__dirname, `../../updates/${fileType}`)
    const options = { multipart: true, formidable: { uploadDir: dirPath, keepExtensions: true } }
    await koaBody(options)(ctx, next)
  } catch (error) {
    ctx.body = { code: 500, message: '上传失败', data: error.message }
  }
}

function renderUploadMessage(ctx) {
  const { fileType = 'file' } = ctx.query
  const { newFilename } = ctx.request.files.file
  ctx.body = { code: 200, message: '上传成功', data: { https: `${ctx.origin}/nodeServer/${fileType}/${newFilename}`, http: `http://*************:3001/${fileType}/${newFilename}` } }
}

//泵房文件上传
async function pumpHouseFileUpload(ctx, next) {
  const { id } = ctx.params //泵房编号
  const { step = 'main' } = ctx.query //所在步骤
  const url = step ? `../../updates/pump_house/${id}/${step}` : `../../updates/pump_house/${id}`
  const dirPath = join(__dirname, url)
  try {
    if (!fs.existsSync(dirPath)) fs.mkdirSync(dirPath, { recursive: true }) //创建泵房文件目录
    const options = {
      multipart: true,
      formidable: {
        uploadDir: dirPath,
        keepExtensions: true,
        onFileBegin: (name, file) => {
          const newFilePath = join(dirPath, file.originalFilename)
          file.filepath = newFilePath
        }
      }
    }
    await koaBody(options)(ctx, next)
  } catch (error) {
    ctx.body = { code: 500, message: '上传失败', data: error.message,dirPath }
  }
}

// 泵房文件上传成功处理
function renderPumpHouseFileUploadMessage(ctx) {

    const { id } = ctx.params //泵房编号
    const { step = 'main' } = ctx.query //所在步骤
    const { originalFilename } = ctx.request.files.file
    const https = `${ctx.origin.replace('http:', 'https:')}/nodeServer/pump_house/${id}/${step}/${encodeURIComponent(originalFilename)}` //https地址
    const http = `http://*************:3001/pump_house/${id}/${step}/${encodeURIComponent(originalFilename)}`
    ctx.body = { code: 200, message: '上传成功', data: { https, http } }  
}
// 查询泵房文件目录
function gainPumpHouseFileCatalog(ctx, next) {
  try {
    const { id } = ctx.params
    const data = {}
    const dirPath = join(__dirname, `../../updates/pump_house/${id}`)
    const dirs = fs.readdirSync(dirPath).filter((file) => Number.isInteger(Number(file)))

    for (let i = 0; i < dirs.length; i++) {
      const dir = dirs[i]
      const filePath = join(dirPath, `/${dir}`)
      const files = fs.readdirSync(filePath).map((file) => {
        // 获取文件类型
        const size = (fs.statSync(filePath + `/${file}`).size / 1024 / 1024).toFixed(2)
        const url = `/nodeServer/pump_house/${id}/${dir}/${encodeURIComponent(file)}`
        const type = mime.lookup(filePath + `/${file}`)
        return { file, url, size, type }
      })
      data[dir] = files
    }
    ctx.body = { code: 200, message: '获取成功', data }
  } catch (error) {
    ctx.body = { code: 500, message: '获取失败', data: error.message }
  }
}

module.exports = { analysisDispose, renderUploadMessage, pumpHouseFileUpload, renderPumpHouseFileUploadMessage, gainPumpHouseFileCatalog }
