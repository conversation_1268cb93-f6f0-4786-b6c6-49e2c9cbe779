const { WX_MP_APPID, WX_MP_APPSECRET, WX_APPID } = process.env
const axios = require('axios')
class WXMPVoucher {
  expirationTime = null // 过期时间
  access_token = null // 接口凭证

  // 判断凭证是否过期，如果过期则重新获取
  async getVoucher() {
    try {
      const newTime = Date.now()
      const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${WX_MP_APPID}&secret=${WX_MP_APPSECRET}`
      if (!this.access_token || newTime >= this.expirationTime) {
        const { data } = await axios.get(url)
        this.access_token = data.access_token
        this.expirationTime = newTime + data.expires_in * 1000
      }
      return this.access_token
    } catch (error) {
      ctx.body = { code: 500, msg: '凭证获取失败' }
    }
  }

  // 获取关注的用户列表
  async getFollowers() {
    try {
      const access_token = await this.getVoucher()
      const url = `https://api.weixin.qq.com/cgi-bin/user/get?access_token=${access_token}`
      const { data } = await axios.get(url)
      return data.data.openid
    } catch (error) {
      ctx.body = { code: 500, msg: '获取关注用户列表失败' }
    }
  }

  // 推送模板消息
  async sendTemplateMessage(template_id, data, xqbm) {
    try {
      const access_token = await this.getVoucher()
      const openids = await this.getFollowers()
      const url = `https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=${access_token}`
      const miniprogram = { appid: WX_APPID, pagepath: `/pages/zone-record/detail?xqbm=${xqbm}&open=1` }
      const res = await Promise.all(openids.map((touser) => axios.post(url, { touser, template_id, data, miniprogram })))
      // console.log(res)
    } catch (error) {
      ctx.body = { code: 500, msg: '推送模板消息失败' }
    }
  }
}

module.exports = new WXMPVoucher()
