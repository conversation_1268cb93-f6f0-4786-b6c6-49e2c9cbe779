const { sql, queryWithParams } = require('../service/index.js')

class FieldWorkController {
  // 维护查询列表
  async list(ctx, next) {
    try {
      const { page, pageSize = 10 } = ctx.request.query
      let sentence
      let sentenceSentence
      const pageNum = Number(page ?? 1) > 0 ? Number(page) : 1
      const sizeNum = Number(pageSize) > 0 ? Number(pageSize) : 10
      const txt = `Id, Zone_Name, Point_Type, Unique_Id, Zone_Code, Coordinates, Type, Created_Time`

      if (page) {
        sentenceSentence = `SELECT COUNT(*) as total FROM dbo.[ValveVerification]`
        sentence = `SELECT ${txt} FROM dbo.[ValveVerification] ORDER BY Created_Time DESC OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`
      } else {
        sentenceSentence = `SELECT COUNT(*) as total FROM dbo.[ValveVerification]`
        sentence = `SELECT ${txt} FROM dbo.[ValveVerification] ORDER BY Created_Time DESC`
      }

      // 查询总数
      const { recordsets: sentenceSets } = await queryWithParams(sentenceSentence)
      const total = sentenceSets[0][0].total
      // 查询分页数据
      const offset = (pageNum - 1) * sizeNum
      const { recordsets } = await queryWithParams(sentence, {
        offset: { type: sql.Int, value: offset },
        sizeNum: { type: sql.Int, value: sizeNum }
      })
      ctx.body = { code: 200, msg: '查询成功', data: { list: recordsets[0], total, page: pageNum, pageSize: sizeNum } }
    } catch (error) {
      ctx.body = { code: 500, mgs: '查询失败', error: error.message }
    }
  }

  // 模糊搜索
  async search(ctx, next) {
    try {
      const { zoneName = '', page = 1, pageSize = 10 } = ctx.request.body || {}
      const pageNum = Number(page) > 0 ? Number(page) : 1
      const sizeNum = Number(pageSize) > 0 ? Number(pageSize) : 10
      let where = ''
      const params = { offset: { type: sql.Int, value: (pageNum - 1) * sizeNum }, sizeNum: { type: sql.Int, value: sizeNum } }
      if (zoneName) {
        where = 'WHERE Zone_Name LIKE @zoneName'
        params.zoneName = { type: sql.VarChar, value: `%${zoneName}%` }
      }
      // 查询总数
      const countSql = `SELECT COUNT(*) as total FROM dbo.[ValveVerification] ${where}`
      const { recordsets: countSets } = await queryWithParams(countSql, params)
      const total = countSets[0][0].total
      // 查询分页数据
      const dataSql = `SELECT Id, Zone_Name, Point_Type, Unique_Id, Zone_Code, Coordinates, Type, Created_Time FROM dbo.[ValveVerification] ${where} ORDER BY Zone_Name OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`
      const { recordsets } = await queryWithParams(dataSql, params)
      ctx.body = {
        code: 200,
        msg: '查询成功',
        data: { list: recordsets[0], total, page: pageNum, pageSize: sizeNum }
      }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', data: error.message }
    }
  }

  // 获取详情
  async gain(ctx, next) {
    try {
      const { id } = ctx.request.query
      if (!id) return (ctx.body = { code: 400, msg: '查询失败', error: '未接收到id 参数' })
      const sentence = `SELECT * FROM dbo.[ValveVerification] WHERE Id = @id`
      const { recordsets } = await queryWithParams(sentence, { id: { type: sql.Int, value: id } })
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}

module.exports = new FieldWorkController()
