const { koaBody } = require('koa-body') //参数解析
const path = require('path')

async function analysisDispose(ctx, next) {
  try {
    const { fileType = 'file' } = ctx.query
    const dirPath = path.join(__dirname, `../../updates/${fileType}`)
    const options = { multipart: true, formidable: { uploadDir: dirPath, keepExtensions: true } }
    await koaBody(options)(ctx, next)
  } catch (error) {
    ctx.body = { code: 500, message: '上传失败', data: error.message }
  }
}

function renderUpadteMessage(ctx) {
  const { fileType = 'file' } = ctx.query
  const { newFilename } = ctx.request.files.file
  ctx.body = { code: 200, message: '上传成功', data: { ip: ctx.ip, https: `${ctx.origin}/nodeServer/${fileType}/${newFilename}`, http: `http://*************:3001/${fileType}/${newFilename}` } }
}

module.exports = { analysisDispose, renderUpadteMessage }
