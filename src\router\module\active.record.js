const Router = require('@koa/router')
const router = new Router({ prefix: '/activeRecord' })
const { getActiveData, activeRecord, gainRecord, gainRecordList, gainActiveLine, gainActiveUser, gainActiveUserLine } = require('../../controller/active.record.controller.js')

router.post('/', activeRecord) // 记录活跃用户
router.get('/', getActiveData, gainRecord) //获取记录
router.get('/list', gainRecordList) //获取记录列表
router.get('/line', getActiveData, gainActiveLine) //获取活跃曲线
router.get('/user', getActiveData, gainActiveUser) //获取活跃用户
router.get('/user/line', getActiveData, gainActiveUserLine) //获取某个活跃曲线

module.exports = router
