const { sql, queryWithParams } = require('../service/index.js')

class GeneralSituationController {
  // 查询概况信息
  async survey(ctx, next) {
    const { date, type, all = 'true' } = ctx.request.query
    const flagType = all == 'true' ? 1 : 2
    try {
      if (!date) return (ctx.body = { code: 400, msg: '查询失败', error: '未接收到日期参数' })
      // 查询指定月份中uploadtime最新的那一批数据
      const sentence = `
        SELECT * FROM dbo.[summary_stat]
        WHERE FORMAT(uploadtime, 'yyyy-MM') = @date
        AND flag = @flagType
        ORDER BY uploadtime DESC;`
      const { recordsets } = await queryWithParams(sentence, { date: { type: sql.NVarChar, value: date }, flagType: { type: sql.Int, value: flagType } })

      let data = {}
      const Ob = {}
      for (let i = 0; i < recordsets[0].length; i++) {
        const item = recordsets[0][i]
        const { pipetype, waterdept, grid, subtype, flag, pipelengthkm, facilitycount } = item
        const source = item.datasource.split('_')[0]
        item.datasource = source

        // 不同维度过滤信息
        if (type == 'source' || !type) {
          if (!Ob[source]) Ob[source] = {}
          if (!Ob[source][item.subtype]) Ob[source][item.subtype] = []
          Ob[source][item.subtype].push({ pipetype, waterdept, flag, grid, pipelengthkm, facilitycount })
        } else if (type == 'waterdept') {
          if (!Ob[waterdept]) Ob[waterdept] = {}
          if (!Ob[waterdept][item.datasource]) Ob[waterdept][item.datasource] = []
          Ob[waterdept][item.datasource].push({ pipetype, grid, subtype, pipelengthkm, facilitycount })
        } else if (type == 'grid') {
          if (!Ob[grid]) Ob[grid] = {}
          if (!Ob[grid][item.datasource]) Ob[grid][item.datasource] = []
          Ob[grid][item.datasource].push({ pipetype, waterdept, subtype, pipelengthkm, facilitycount })
        } else {
          ctx.body = { code: 400, msg: 'type参数错误' }
          return
        }
      }

      if (type == 'source' || !type) {
        data = Object.entries(Ob).map(([key, value]) => ({ datasource: key, data: Object.entries(value).map(([key, value]) => ({ subtype: key, dataValues: value })) }))
      } else {
        data = Object.entries(Ob).map(([key, value]) => ({ datasource: key, data: Object.entries(value).map(([key, value]) => ({ source: key, dataValues: value })) }))
      }

      ctx.body = { code: 200, msg: '查询成功', data: data }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }

  // 查询数据月份
  async dateSurvey(ctx, next) {
    try {
      const sentence = `SELECT FORMAT(uploadtime, 'yyyy-MM') AS year_month, COUNT(*) AS record_count FROM dbo.[summary_stat] GROUP BY FORMAT(uploadtime, 'yyyy-MM') ORDER BY year_month;`
      const { recordsets } = await queryWithParams(sentence, {})
      ctx.body = { code: 200, msg: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 500, msg: '查询失败', error: error.message }
    }
  }
}

module.exports = new GeneralSituationController()
