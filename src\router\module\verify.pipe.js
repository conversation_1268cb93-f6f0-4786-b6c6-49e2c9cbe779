const Router = require('@koa/router')
const router = new Router({ prefix: '/verify' })
const controller = require('../../controller/verify.pipe.controller.js')

// 管材核查专项
router.get('/', controller.gain)
router.get('/statistics', controller.statistics)
router.put('/', controller.update)
router.post('/', controller.distinguish)
router.post('/list', controller.list)
router.post('/search', controller.search)

module.exports = router
