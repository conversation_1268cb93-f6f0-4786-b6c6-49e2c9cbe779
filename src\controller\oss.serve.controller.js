// 引入阿里云OSS相关模块
const OSS = require('ali-oss') // 阿里云OSS SDK主模块
const { STS } = require('ali-oss') // 阿里云STS（Security Token Service）模块，用于获取临时访问凭证
const { getCredential } = require('ali-oss/lib/common/signUtils') // 获取凭证信息的工具函数
const { getStandardRegion } = require('ali-oss/lib/common/utils/getStandardRegion') // 获取标准地域名称的工具函数
const { policy2Str } = require('ali-oss/lib/common/utils/policy2Str') // 将策略对象转换为字符串的工具函数

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小字符串
 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * OSS服务控制器类
 * 负责处理阿里云OSS相关的业务逻辑，主要提供临时访问凭证的获取功能
 */
class OssServeController {
  // 获取临时访问凭证
  async voucher(ctx, next) {
    let bucket
    const { privately = '0', bucket: lastBucket } = ctx.query

    if (lastBucket) {
      bucket = lastBucket
    } else {
      if (!['0', '1'].includes(privately)) return (ctx.body = { code: 400, msg: 'privately参数错误' })
      bucket = privately == '1' ? 'ft-oss-data' : 'ft-oss-image'
    }

    try {
      // 初始化STS客户端，使用环境变量中的访问密钥
      let sts = new STS({ accessKeyId: process.env.OSS_ACCESS_KEY_ID, accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET })

      // 调用assumeRole接口获取STS临时访问凭证
      // 参数说明：
      // - process.env.OSS_ROLE_ARN: 角色ARN，定义了临时凭证的权限范围
      // - '': 会话名称（可选）
      // - '3600': 凭证有效期（秒），此处设置为1小时
      // - 'futRam': 会话策略名称
      const result = await sts.assumeRole(process.env.OSS_ROLE_ARN, '', '3600', 'futRam')

      // 从STS响应中提取临时访问凭证的三个关键组件
      const accessKeyId = result.credentials.AccessKeyId // 临时访问密钥ID
      const accessKeySecret = result.credentials.AccessKeySecret // 临时访问密钥
      const securityToken = result.credentials.SecurityToken // 安全令牌

      // 使用临时凭证初始化OSS客户端
      const client = new OSS({
        bucket, // 目标存储桶名称
        region: process.env.OSS_REGION, // 存储桶所在地域
        accessKeyId, // 临时访问密钥ID
        accessKeySecret, // 临时访问密钥
        stsToken: securityToken, // STS安全令牌
        refreshSTSTokenInterval: 0, // 禁用自动刷新令牌（设置为0）
        // 令牌刷新回调函数（当前未启用自动刷新）
        refreshSTSToken: async () => {
          const { accessKeyId, accessKeySecret, securityToken } = await client.getCredential()
          return { accessKeyId, accessKeySecret, stsToken: securityToken }
        }
      })

      // 计算签名过期时间：当前时间 + 10分钟
      const date = new Date() // 当前时间
      const expirationDate = new Date(date) // 复制当前时间用于计算过期时间
      expirationDate.setMinutes(date.getMinutes() + 10) // 设置过期时间为10分钟后

      /**
       * 将数字补齐为两位数字符串
       * @param {number} num - 需要补齐的数字
       * @returns {string} 补齐后的字符串
       */
      function padTo2Digits(num) {
        return num.toString().padStart(2, '0')
      }

      /**
       * 将日期对象格式化为符合ISO 8601标准的UTC时间字符串
       * 格式：YYYYMMDDTHHMMSSZ
       * @param {Date} date - 需要格式化的日期对象
       * @returns {string} 格式化后的UTC时间字符串
       */
      function formatDateToUTC(date) {
        return date.getUTCFullYear() + padTo2Digits(date.getUTCMonth() + 1) + padTo2Digits(date.getUTCDate()) + 'T' + padTo2Digits(date.getUTCHours()) + padTo2Digits(date.getUTCMinutes()) + padTo2Digits(date.getUTCSeconds()) + 'Z'
      }

      // 格式化过期时间为UTC字符串
      const formattedDate = formatDateToUTC(expirationDate)

      // 生成OSS凭证字符串，用于签名验证
      // 参数：日期部分、标准地域名称、访问密钥ID
      const credential = getCredential(
        formattedDate.split('T')[0], // 提取日期部分（YYYYMMDD）
        getStandardRegion(client.options.region), // 获取标准地域格式
        client.options.accessKeyId // 访问密钥ID
      )

      // 创建上传策略对象
      // 策略定义了上传的限制条件和有效期
      const policy = {
        expiration: expirationDate.toISOString(), // 策略过期时间（ISO格式）
        conditions: [
          // 上传条件数组
          { bucket }, // 限制上传到指定存储桶
          { 'x-oss-credential': credential }, // OSS凭证
          { 'x-oss-signature-version': 'OSS4-HMAC-SHA256' }, // 签名版本
          { 'x-oss-date': formattedDate } // 请求日期
        ]
      }

      // 如果使用STS临时凭证，需要在策略中添加安全令牌
      if (client.options.stsToken) {
        policy.conditions.push({ 'x-oss-security-token': client.options.stsToken })
      }

      // 使用OSS客户端生成POST对象策略的V4签名
      const signature = client.signPostObjectPolicyV4(policy, date)

      // 构造成功响应，返回前端上传所需的所有参数
      ctx.body = {
        code: 200,
        message: '获取STS Token成功',
        data: {
          // OSS上传端点URL
          host: `http://${client.options.bucket}.${client.options.region}.aliyuncs.com`,
          // Base64编码的上传策略
          policy: Buffer.from(policy2Str(policy), 'utf8').toString('base64'),
          success_action_status: '200',
          // 签名版本标识
          'x-oss-signature-version': 'OSS4-HMAC-SHA256',
          // OSS凭证字符串
          'x-oss-credential': credential,
          // 格式化的请求日期
          'x-oss-date': formattedDate,
          // 计算得出的签名
          'x-oss-signature': signature,
          // STS安全令牌（如果使用临时凭证）
          'x-oss-security-token': client.options.stsToken
        },
        OSS_BASEURL: privately == '1' ? 'http://www.privately.szwgft.cn' : 'http://www.resource.szwgft.cn'
      }
    } catch (error) {
      // 捕获异常并返回错误响应
      ctx.body = {
        code: 500,
        message: '获取STS Token失败',
        data: error.message
      }
    }
  }

  // 获取资源下载地址
  async gain(ctx) {
    const { fileName, expireTime = 3600 } = ctx.query

    // 验证必需参数
    if (!fileName) {
      return (ctx.body = { code: 400, message: '缺少必需参数：fileName', data: null })
    }

    try {
      // 初始化OSS客户端，使用主账号密钥访问私有bucket
      // 先尝试不使用V4签名版本，避免签名错误
      const client = new OSS({
        accessKeyId: process.env.OSS_ACCESS_KEY_ID,
        accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
        bucket: 'ft-oss-data', // 私有bucket
        region: process.env.OSS_REGION,
        secure: true // 使用HTTPS
        // 暂时不使用V4签名版本，使用默认的V1签名
        // authorizationV4: true
      })

      // 检查文件是否存在
      try {
        await client.head(fileName)
      } catch (error) {
        if (error.code === 'NoSuchKey') {
          return (ctx.body = { code: 404, message: '文件不存在', data: null })
        }
        throw error
      }

      // 生成带签名的下载URL
      // 使用V1签名版本的 signatureUrl 方法
      const signedUrl = client.signatureUrl(fileName, {
        method: 'GET',
        expires: parseInt(expireTime)
      })

      // 返回成功响应
      ctx.body = {
        code: 200,
        message: '获取下载链接成功',
        data: {
          downloadUrl: signedUrl,
          fileName: fileName,
          expireTime: parseInt(expireTime),
          expireAt: new Date(Date.now() + parseInt(expireTime) * 1000 + 8 * 60 * 60 * 1000).toISOString()
        }
      }
    } catch (error) {
      console.error('获取下载链接失败:', error)
      ctx.body = {
        code: 500,
        message: '获取下载链接失败',
        data: error.message
      }
    }
  }

  // 查询文件列表
  async list(ctx) {
    const { prefix = '', maxKeys = 100, marker = '', bucket: targetBucket, privately = '1' } = ctx.query

    // 确定要查询的bucket
    let bucket
    if (targetBucket) {
      bucket = targetBucket
    } else {
      bucket = privately === '1' ? 'ft-oss-data' : 'ft-oss-image'
    }

    try {
      // 初始化OSS客户端
      const client = new OSS({
        accessKeyId: process.env.OSS_ACCESS_KEY_ID,
        accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
        bucket: bucket,
        region: process.env.OSS_REGION,
        secure: true
        // 暂时不使用V4签名版本，使用默认的V1签名
        // authorizationV4: true
      })

      // 构建查询参数
      const listParams = {
        prefix: prefix, // 文件前缀过滤
        'max-keys': Math.min(parseInt(maxKeys), 1000), // 限制最大返回数量，最多1000
        marker: marker // 分页标记
      }

      // 查询文件列表
      const result = await client.list(listParams)

      // 处理文件列表数据
      const files = result.objects
        ? result.objects.map((obj) => ({
            name: obj.name,
            size: obj.size,
            lastModified: obj.lastModified,
            etag: obj.etag,
            type: obj.type,
            storageClass: obj.storageClass,
            // 添加文件扩展名和MIME类型
            extension: obj.name.split('.').pop().toLowerCase(),
            mimeType: require('mime-types').lookup(obj.name) || 'application/octet-stream',
            // 格式化文件大小
            sizeFormatted: formatFileSize(obj.size),
            // 相对路径（去除前缀）
            relativePath: prefix ? obj.name.replace(new RegExp(`^${prefix}`), '') : obj.name
          }))
        : []

      // 返回成功响应
      ctx.body = {
        code: 200,
        message: '获取文件列表成功',
        data: {
          bucket: bucket,
          prefix: prefix,
          files: files,
          count: files.length,
          isTruncated: result.isTruncated || false,
          nextMarker: result.nextMarker || null,
          // 分页信息
          pagination: {
            currentMarker: marker,
            nextMarker: result.nextMarker || null,
            hasMore: result.isTruncated || false,
            maxKeys: parseInt(maxKeys)
          }
        }
      }
    } catch (error) {
      console.error('获取文件列表失败:', error)
      ctx.body = {
        code: 500,
        message: '获取文件列表失败',
        data: error.message
      }
    }
  }
}

// 导出控制器实例，供路由使用
module.exports = new OssServeController()
