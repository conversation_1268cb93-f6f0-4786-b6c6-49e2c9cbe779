const { sql, query, queryWithParams } = require('../service/index.js')
const axios = require('axios')
const jwt = require('jsonwebtoken')

const { WX_APPSECRET, WX_APPID } = process.env

class UserController {
  // 登录
  async login(ctx) {
    try {
      const { username, password } = ctx.request.body
      if (!username || !password) ctx.body = { code: 400, data: '用户名或密码缺失' }
      const sentence = `SELECT * FROM dbo.[User] WHERE Username = @username AND Password = @password`
      const { recordsets } = await queryWithParams(sentence, { username: { type: sql.VarChar, value: username }, password: { type: sql.VarChar, value: password } })
      if (recordsets[0].length === 0) return (ctx.body = { code: 400, data: '登录失败，请检查账号和密码' })
      const { id, Username, Station, Path, Name, Jurisdiction } = recordsets[0][0]
      const parcel = { 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier': id, 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': Username }
      const token = jwt.sign(parcel, process.env.JWT_SECRET, { expiresIn: 60 * 60 * 365, algorithm: 'HS256' })
      const userInfo = { id, Username, station: Station, path: Path, name: Name, Jurisdiction }
      ctx.body = { code: 200, data: { token, userInfo } }
    } catch (error) {
      ctx.body = { code: 500, error: error.message }
    }
  }

  // 微信登录
  async WXLogin(ctx) {
    const { code } = ctx.request.body
    try {
      if (!code) ctx.body = { code: 400, data: 'code 缺失' }
      const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${WX_APPID}&secret=${WX_APPSECRET}&js_code=${code}&grant_type=authorization_code`
      const result = await axios.get(url)
      const { openid, session_key } = result.data
      const sentence = `SELECT * FROM dbo.[User] WHERE WXCode = @openid`
      const { recordsets } = await queryWithParams(sentence, { openid: { type: sql.VarChar, value: openid } })
      if (recordsets[0].length > 0) {
        const { id, Username, Station, Path, Name, Jurisdiction } = recordsets[0][0]
        const parcel = { 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier': id, 'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': Username }
        const token = jwt.sign(parcel, process.env.JWT_SECRET, { expiresIn: 60 * 60 * 365, algorithm: 'HS256' })
        const userInfo = { id, Username, station: Station, path: Path, name: Name, Jurisdiction }
        ctx.body = { code: 200, data: { token, userInfo } }
      } else {
        ctx.body = { code: 400, data: '未绑定账号' }
      }
    } catch (error) {
      ctx.body = { code: 500, error: error.message }
    }
  }

  // 微信号绑定用户
  async WXBinding(ctx) {
    const { username, password, code } = ctx.request.body
    try {
      if (!username || !password) return (ctx.body = { code: 400, data: '请输入账号和密码' })
      const sentence = `SELECT * FROM dbo.[User] WHERE Username = @username AND Password = @password`
      const { recordsets } = await queryWithParams(sentence, { username: { type: sql.VarChar, value: username }, password: { type: sql.VarChar, value: password } })
      if (recordsets[0].length === 0) return (ctx.body = { code: 400, data: '登录失败，请检查账号和密码' })
      const userInfo = recordsets[0][0]
      if (userInfo.WXCode) return (ctx.body = { code: 400, data: '该账号已绑定微信,若非本人操作,请联系管理员' })
      const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${WX_APPID}&secret=${WX_APPSECRET}&js_code=${code}&grant_type=authorization_code`
      const result = await axios.get(url)
      const { openid, session_key } = result.data
      const sentence2 = `UPDATE dbo.[User] SET WXCode = @openid WHERE id = @id`
      await queryWithParams(sentence2, { openid: { type: sql.VarChar, value: openid }, id: { type: sql.Int, value: userInfo.id } })
      ctx.body = { code: 200, data: '绑定成功' }
    } catch (error) {
      ctx.body = { code: 500, msg: '服务器错误,绑定失败', error: error.message }
    }
  }
}

module.exports = new UserController()
