const Router = require('@koa/router')
const controller = require('../../controller/app.version.control.controller.js')
const router = new Router({ prefix: '/versionControl' })

router.post('/create', controller.create) //版本创建
router.delete('/delete', controller.delete) //版本删除
router.put('/update', controller.update) //版本更新
router.get('/list', controller.list) //版本列表
router.get('/compare', controller.compare) //更新检查

module.exports = router
