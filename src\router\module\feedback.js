const Router = require('@koa/router')
const controller = require('../../controller/feedback.controller.js')
const router = new Router({ prefix: '/feedback' })

// 问题反馈
router.post('/', controller.feedback.create)
router.post('/createRecord', controller.feedback.createRecord)
router.delete('/:id', controller.feedback.delete)
router.put('/', controller.feedback.update)
router.get('/list', controller.feedback.list)
router.get('/listRecord', controller.feedback.listRecord)
router.get('/:id', controller.feedback.gain)
router.get('/hide/:id', controller.feedback.hide)
router.get('/pending/search', controller.feedback.search)

// 反馈回复
router.post('/reply', controller.reply.create)
router.delete('/reply/:id', controller.reply.delete)
router.put('/reply', controller.reply.update)
router.get('/reply/:id', controller.reply.list)
router.get('/reply/hide/:id', controller.reply.hide)
router.get('/reply/pending/count', controller.reply.pendingCount)

// 反馈
router.post('/read', controller.read.create)
router.get('/read/:ReplyID', controller.read.gain)
router.get('/read/list/:ReplyID', controller.read.list)

module.exports = router
