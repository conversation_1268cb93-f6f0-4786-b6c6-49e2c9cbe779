function compare(a, b) {
  const _a = a.split('.').map(Number)
  const _b = b.split('.').map(Number)
  for (let i = 0; i < _a.length; i++) {
    if (_a[i] > _b[i]) return true
    if (_a[i] < _b[i]) return false
  }
  return false
}

function isNumber(value) {
  return typeof value === 'number' && !isNaN(value)
}

// 生成版本编码
function createVersionCode(version) {
  const [major, minor, patch] = version.split('.')
  const newMinor = ('0000' + minor).slice(-4)
  const newPatch = ('0000' + patch).slice(-4)
  return Number.parseInt(major + newMinor + newPatch)
}
module.exports = { compare, isNumber, createVersionCode }
