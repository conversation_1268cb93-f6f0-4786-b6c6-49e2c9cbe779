const Router = require('@koa/router')
const controller = require('../../controller/dictionaries.controller.js')
const router = new Router({ prefix: '/dictionaries' })

// 字典管理
router.post('/', controller.create)
router.delete('/', controller.delete)
router.put('/', controller.update)
router.get('/:type', controller.gain)
router.get('/list/:type', controller.list)
router.get('/type/list', controller.typeList)

module.exports = router
